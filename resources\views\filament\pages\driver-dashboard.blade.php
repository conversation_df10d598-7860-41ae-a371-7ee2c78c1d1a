<x-filament-panels::page>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <!-- CSS for responsive view mode -->
    <style>
        /* Hide table view on mobile by default */
        @media (max-width: 767px) {
            .table-view-container {
                display: none !important;
            }

            .compact-view-container {
                display: block !important;
            }
        }

        /* Hide compact view on desktop by default */
        @media (min-width: 768px) {
            .table-view-container {
                display: block !important;
            }

            .compact-view-container {
                display: none !important;
            }
        }

        /* Override when JavaScript sets specific view mode */
        .force-table-view .table-view-container {
            display: block !important;
        }

        .force-table-view .compact-view-container {
            display: none !important;
        }

        .force-compact-view .table-view-container {
            display: none !important;
        }

        .force-compact-view .compact-view-container {
            display: block !important;
        }
    </style>

    <!-- Mobile View Mode Detection -->
    <script>
        // Global variables for Livewire integration
        let livewireReady = false;
        let pendingViewModeChanges = [];

        // Check if Livewire is available
        function isLivewireReady() {
            const ready = typeof window.Livewire !== 'undefined' && window.Livewire.find && livewireReady;
            console.log('Livewire ready check:', ready, {
                livewireExists: typeof window.Livewire !== 'undefined',
                findExists: window.Livewire?.find,
                livewireReady: livewireReady
            });
            return ready;
        }

        // Debug function to check available methods
        function debugLivewireComponent() {
            try {
                const wireElement = document.querySelector('[wire\\:id]');
                if (wireElement) {
                    const wireId = wireElement.getAttribute('wire:id');
                    console.log('Wire ID:', wireId);
                    const component = window.Livewire.find(wireId);
                    console.log('Component found:', component);
                    if (component) {
                        console.log('Available methods:', Object.getOwnPropertyNames(component));
                        console.log('setViewMode available:', typeof component.setViewMode);
                        console.log('toggleViewMode available:', typeof component.toggleViewMode);
                    }
                }
            } catch (error) {
                console.error('Debug error:', error);
            }
        }

        // Execute pending view mode changes when Livewire is ready
        function executePendingChanges() {
            if (isLivewireReady() && pendingViewModeChanges.length > 0) {
                pendingViewModeChanges.forEach(change => {
                    try {
                        if (window.Livewire.find) {
                            const component = window.Livewire.find(document.querySelector('[wire\\:id]')
                                ?.getAttribute('wire:id'));
                            if (component && component.call) {
                                component.call('setViewMode', change.mode);
                            }
                        }
                    } catch (error) {
                        console.warn('Failed to execute pending view mode change:', error);
                    }
                });
                pendingViewModeChanges = [];
            }
        }

        // Safe wrapper for Livewire method calls
        function safeSetViewMode(mode) {
            try {
                // Use Livewire.dispatch for event-based communication
                if (typeof window.Livewire !== 'undefined' && window.Livewire.dispatch) {
                    window.Livewire.dispatch('setViewMode', {
                        mode: mode
                    });
                    return true;
                }

                // Fallback: Try direct component access
                if (isLivewireReady()) {
                    const wireElement = document.querySelector('[wire\\:id]');
                    if (wireElement) {
                        const wireId = wireElement.getAttribute('wire:id');
                        const component = window.Livewire.find(wireId);

                        if (component && component.call) {
                            component.call('setViewMode', mode);
                            return true;
                        }
                    }
                }
            } catch (error) {
                console.warn('Failed to call setViewMode:', error);
            }

            // Queue the change for later execution
            pendingViewModeChanges.push({
                mode: mode
            });
            return false;
        }

        // Screen size detection and auto view mode setting
        function detectScreenSize() {
            const isMobile = window.innerWidth < 768;
            const currentMode = @js($this->viewMode);

            // Auto-set view mode based on screen size on initial load
            if (currentMode === 'auto' || currentMode === null) {
                const newMode = isMobile ? 'compact' : 'table';

                // Try to set view mode, if fails, apply CSS directly
                if (!safeSetViewMode(newMode)) {
                    // Fallback: apply CSS classes directly
                    applyViewModeClasses(newMode);
                }
            }

            // Update toggle button visibility and apply CSS classes
            updateToggleButtonVisibility(isMobile);
            applyViewModeClasses(currentMode);
        }

        function applyViewModeClasses(mode) {
            const container = document.querySelector('.space-y-6');
            if (container) {
                // Remove existing classes
                container.classList.remove('force-table-view', 'force-compact-view');

                // Apply appropriate class based on mode
                if (mode === 'table') {
                    container.classList.add('force-table-view');
                } else if (mode === 'compact') {
                    container.classList.add('force-compact-view');
                }
            }
        }

        function updateToggleButtonVisibility(isMobile) {
            const toggleButton = document.getElementById('view-toggle-button');
            if (toggleButton) {
                if (isMobile) {
                    toggleButton.classList.add('hidden');
                    toggleButton.classList.remove('inline-flex');
                } else {
                    toggleButton.classList.remove('hidden');
                    toggleButton.classList.add('inline-flex');
                }
            }
        }

        // Livewire initialization and event listeners
        document.addEventListener('livewire:init', () => {
            livewireReady = true;
            console.log('Livewire initialized');
            executePendingChanges();
        });

        document.addEventListener('livewire:navigated', () => {
            livewireReady = true;
            console.log('Livewire navigated');
            executePendingChanges();
        });

        // Run on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for Livewire to initialize
            setTimeout(() => {
                if (typeof window.Livewire !== 'undefined') {
                    livewireReady = true;
                    executePendingChanges();
                }
                detectScreenSize();
            }, 100);
        });

        // Run on window resize with debouncing
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                const isMobile = window.innerWidth < 768;
                const currentMode = @js($this->viewMode);

                // Auto-switch view mode on resize
                if (isMobile && currentMode === 'table') {
                    safeSetViewMode('compact');
                } else if (!isMobile && currentMode === 'compact') {
                    safeSetViewMode('table');
                }

                updateToggleButtonVisibility(isMobile);
            }, 250); // 250ms debounce
        });

        // Livewire hook to update button after view mode changes
        document.addEventListener('livewire:updated', function() {
            const isMobile = window.innerWidth < 768;
            const currentMode = @js($this->viewMode);
            updateToggleButtonVisibility(isMobile);
            updateMobileToggleButton();
            applyViewModeClasses(currentMode);

            // Execute any pending changes after update
            executePendingChanges();
        });

        // Additional Livewire event listeners
        document.addEventListener('livewire:init', function() {
            console.log('Livewire initialized');
            livewireReady = true;
            debugLivewireComponent();
            detectScreenSize();
        });

        document.addEventListener('livewire:navigated', function() {
            console.log('Livewire navigated');
            livewireReady = true;
            debugLivewireComponent();
            detectScreenSize();
        });

        // Fallback for older Livewire versions
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                if (typeof window.Livewire !== 'undefined') {
                    console.log('Livewire detected via DOMContentLoaded');
                    livewireReady = true;
                    debugLivewireComponent();
                    detectScreenSize();
                }
            }, 1000);
        });

        // Mobile toggle function
        function toggleMobileView() {
            try {
                // Use Livewire.dispatch for event-based communication
                if (typeof window.Livewire !== 'undefined' && window.Livewire.dispatch) {
                    window.Livewire.dispatch('toggleViewMode');
                    return;
                }

                // Fallback: Try direct component access
                if (isLivewireReady()) {
                    const wireElement = document.querySelector('[wire\\:id]');
                    if (wireElement) {
                        const wireId = wireElement.getAttribute('wire:id');
                        const component = window.Livewire.find(wireId);

                        if (component && component.call) {
                            component.call('toggleViewMode').then(() => {
                                const newMode = @js($this->viewMode);
                                applyViewModeClasses(newMode);
                            }).catch(error => {
                                console.warn('Failed to toggle view mode:', error);
                            });
                        }
                    }
                }
            } catch (error) {
                console.warn('Failed to call toggleViewMode:', error);
            }
        }

        // Update mobile toggle button text and icon
        function updateMobileToggleButton() {
            const currentMode = @js($this->viewMode);
            const toggleText = document.getElementById('mobile-toggle-text');
            const toggleIcon = document.getElementById('mobile-toggle-icon');

            if (toggleText && toggleIcon) {
                if (currentMode === 'compact') {
                    toggleText.textContent = 'Tabel';
                    toggleIcon.innerHTML = `
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18M3 18h18M3 6h18"></path>
                        </svg>
                    `;
                } else {
                    toggleText.textContent = 'Kompak';
                    toggleIcon.innerHTML = `
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                        </svg>
                    `;
                }
            }
        }

        // Initialize mobile toggle button on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateMobileToggleButton();
        });
    </script>

    <div class="space-y-6">
        <!-- Welcome Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                    <x-heroicon-o-truck class="h-8 w-8 text-amber-500" />
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Selamat Datang, {{ Auth::user()->name }}
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Dashboard pengiriman untuk driver
                    </p>
                </div>
            </div>
        </div>

        <!-- KPI Cards -->
        @php
            $stats = $this->getDeliveryStats();
        @endphp

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <!-- Total Deliveries -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-clipboard-document-list class="h-6 w-6 text-blue-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Total Pengiriman
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['total_deliveries'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Deliveries -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-clock class="h-6 w-6 text-yellow-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Menunggu Muat
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['pending_deliveries'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- In Progress Deliveries -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-arrow-path class="h-6 w-6 text-orange-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Sedang Muat
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['in_progress_deliveries'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completed Deliveries -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-check-circle class="h-6 w-6 text-green-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Selesai
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['completed_deliveries'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Volume -->
            {{-- <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-beaker class="h-6 w-6 text-purple-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Total Volume
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ number_format($stats['total_volume'], 0, ',', '.') }} L
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div> --}}

            <!-- Pending Allowances -->
            {{-- <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-banknotes class="h-6 w-6 text-red-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Uang Jalan Pending
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['pending_allowances'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div> --}}

            <!-- Pending Allowance Approvals -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-clock class="h-6 w-6 text-amber-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Approval Uang Jalan Pending
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['pending_allowance_approvals'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Approved Allowances -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-check-badge class="h-6 w-6 text-emerald-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Uang Jalan Disetujui
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['approved_allowances'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Delivery Approvals -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-exclamation-triangle class="h-6 w-6 text-yellow-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Approval Pengiriman Pending
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['pending_delivery_approvals'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Approved Deliveries -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-shield-check class="h-6 w-6 text-green-500" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    Pengiriman Disetujui
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['approved_deliveries'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivery Orders Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                            Daftar Pengiriman Saya
                        </h3>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            Kelola dan pantau status pengiriman yang ditugaskan kepada Anda
                        </p>
                    </div>

                    <!-- Mobile Toggle Button -->
                    <div class="md:hidden">
                        <button type="button" id="mobile-view-toggle" onclick="toggleMobileView()"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <span id="mobile-toggle-icon">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                </svg>
                            </span>
                            <span id="mobile-toggle-text">Tabel</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Table View Container -->
            <div class="table-view-container p-6">
                {{ $this->table }}
            </div>

            <!-- Compact View Container -->
            <div class="compact-view-container p-4 space-y-4">
                @forelse($this->getDeliveryOrders() as $delivery)
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
                        <!-- Header Row -->
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div
                                        class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                        <x-heroicon-o-truck class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-semibold text-gray-900 dark:text-white">
                                        {{ $delivery->kode }}
                                    </h4>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $delivery->transaksi->pelanggan->nama }}
                                    </p>
                                </div>
                            </div>

                            <!-- Status Badges -->
                            <div class="flex flex-wrap gap-1">
                                <span
                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @if ($delivery->status_muat === 'pending') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                        @elseif($delivery->status_muat === 'muat') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                        @else bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @endif">
                                    @if ($delivery->status_muat === 'pending')
                                        Menunggu
                                    @elseif($delivery->status_muat === 'muat')
                                        Sedang Muat
                                    @else
                                        Selesai
                                    @endif
                                </span>
                            </div>
                        </div>

                        <!-- Details Grid -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                            <!-- Vehicle Info -->
                            <div class="flex items-center space-x-2">
                                <x-heroicon-o-truck class="w-4 h-4 text-gray-400" />
                                <span class="text-gray-600 dark:text-gray-300">
                                    {{ $delivery->kendaraan->no_pol_kendaraan ?? 'N/A' }}
                                </span>
                            </div>

                            <!-- Date -->
                            <div class="flex items-center space-x-2">
                                <x-heroicon-o-calendar class="w-4 h-4 text-gray-400" />
                                <span class="text-gray-600 dark:text-gray-300">
                                    {{ $delivery->tanggal_delivery ? \Carbon\Carbon::parse($delivery->tanggal_delivery)->format('d/m/Y') : 'N/A' }}
                                </span>
                            </div>

                            <!-- Volume -->
                            <div class="flex items-center space-x-2">
                                <x-heroicon-o-beaker class="w-4 h-4 text-gray-400" />
                                <span class="text-gray-600 dark:text-gray-300">
                                    {{ number_format($delivery->volume_do, 0, ',', '.') }} L
                                </span>
                            </div>

                            <!-- Address -->
                            <div class="flex items-center space-x-2">
                                <x-heroicon-o-map-pin class="w-4 h-4 text-gray-400" />
                                <span class="text-gray-600 dark:text-gray-300 truncate">
                                    {{ $delivery->transaksi->pelanggan->alamatPelanggan->first()->alamat ?? 'Alamat tidak tersedia' }}
                                </span>
                            </div>
                        </div>

                        <!-- Approval Status Row -->
                        <div
                            class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                            <div class="flex flex-wrap gap-2">
                                <!-- Uang Jalan Status -->
                                <div class="flex items-center space-x-2">
                                    <x-heroicon-o-banknotes class="w-4 h-4 text-gray-400" />
                                    <span class="text-xs text-gray-500 dark:text-gray-400">Uang Jalan:</span>
                                    <span
                                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            @if ($delivery->uangJalan?->approval_status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                            @elseif($delivery->uangJalan?->approval_status === 'approved') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                            @elseif($delivery->uangJalan?->approval_status === 'rejected') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                            @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                                        @if ($delivery->uangJalan?->approval_status === 'pending')
                                            Menunggu
                                        @elseif($delivery->uangJalan?->approval_status === 'approved')
                                            Disetujui
                                        @elseif($delivery->uangJalan?->approval_status === 'rejected')
                                            Ditolak
                                        @else
                                            N/A
                                        @endif
                                    </span>
                                </div>

                                <!-- Pengiriman Status -->
                                <div class="flex items-center space-x-2">
                                    <x-heroicon-o-truck class="w-4 h-4 text-gray-400" />
                                    <span class="text-xs text-gray-500 dark:text-gray-400">Pengiriman:</span>
                                    <span
                                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            @if ($delivery->pengirimanDriver?->approval_status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                            @elseif($delivery->pengirimanDriver?->approval_status === 'approved') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                            @elseif($delivery->pengirimanDriver?->approval_status === 'rejected') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                            @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                                        @if ($delivery->pengirimanDriver?->approval_status === 'pending')
                                            Menunggu
                                        @elseif($delivery->pengirimanDriver?->approval_status === 'approved')
                                            Disetujui
                                        @elseif($delivery->pengirimanDriver?->approval_status === 'rejected')
                                            Ditolak
                                        @else
                                            N/A
                                        @endif
                                    </span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex flex-wrap gap-1">
                                <!-- View Details -->
                                <a href="{{ route('filament.admin.pages.driver-delivery-detail', ['record' => $delivery->id]) }}"
                                    class="inline-flex items-center px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <x-heroicon-o-eye class="w-3 h-3 mr-1" />
                                    Detail
                                </a>

                                <!-- Status Update -->
                                @if ($delivery->status_muat !== 'selesai')
                                    <button type="button" onclick="updateStatus('{{ $delivery->id }}')"
                                        class="inline-flex items-center px-2 py-1 border border-yellow-300 dark:border-yellow-600 rounded text-xs font-medium text-yellow-700 dark:text-yellow-300 bg-yellow-50 dark:bg-yellow-900 hover:bg-yellow-100 dark:hover:bg-yellow-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                        <x-heroicon-o-pencil-square class="w-3 h-3 mr-1" />
                                        Update
                                    </button>
                                @endif

                                <!-- Approval Actions -->
                                @if ($delivery->uangJalan && $delivery->uangJalan->approval_status === 'pending')
                                    <button type="button" onclick="approveAllowance('{{ $delivery->id }}')"
                                        class="inline-flex items-center px-2 py-1 border border-green-300 dark:border-green-600 rounded text-xs font-medium text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900 hover:bg-green-100 dark:hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        <x-heroicon-o-banknotes class="w-3 h-3 mr-1" />
                                        ACC UJ
                                    </button>
                                @endif

                                @if ($delivery->pengirimanDriver && $delivery->pengirimanDriver->approval_status === 'pending')
                                    <button type="button" onclick="approveDelivery('{{ $delivery->id }}')"
                                        class="inline-flex items-center px-2 py-1 border border-blue-300 dark:border-blue-600 rounded text-xs font-medium text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900 hover:bg-blue-100 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <x-heroicon-o-truck class="w-3 h-3 mr-1" />
                                        ACC Kirim
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8">
                        <x-heroicon-o-truck class="mx-auto h-12 w-12 text-gray-400" />
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Tidak ada pengiriman
                        </h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Belum ada pengiriman yang
                            ditugaskan kepada Anda.</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- JavaScript for Compact View Actions -->
    <script>
        // iOS Detection (including Chrome iOS)
        function isIOS() {
            const userAgent = navigator.userAgent;
            const platform = navigator.platform;

            // Standard iOS detection
            if (/iPad|iPhone|iPod/.test(userAgent)) {
                return true;
            }

            // iPad Pro detection (reports as MacIntel)
            if (platform === 'MacIntel' && navigator.maxTouchPoints > 1) {
                return true;
            }

            // Chrome iOS specific detection
            if (/CriOS/.test(userAgent)) {
                return true;
            }

            // Firefox iOS specific detection
            if (/FxiOS/.test(userAgent)) {
                return true;
            }

            // Additional iOS detection for newer devices
            if (/iPhone|iPad|iPod|iOS/.test(userAgent) && /AppleWebKit/.test(userAgent)) {
                return true;
            }

            return false;
        }

        // Global iOS fix for ALL Livewire requests
        if (isIOS()) {
            console.log('iOS detected, applying global Livewire fixes');

            // Override ALL Livewire requests globally
            document.addEventListener('DOMContentLoaded', function() {
                if (window.Livewire) {
                    // Hook into ALL Livewire requests
                    window.Livewire.hook('request', ({
                        uri,
                        options,
                        payload,
                        respond,
                        succeed,
                        fail
                    }) => {
                        console.log('Global Livewire request hook:', {
                            uri,
                            options,
                            payload
                        });

                        // Fix payload structure for iOS
                        if (payload && typeof payload === 'object') {
                            // Add _method to main payload
                            if (!payload._method) {
                                payload._method = 'POST';
                                console.log('Added _method to main payload');
                            }

                            // Fix components array
                            if (payload.components && Array.isArray(payload.components)) {
                                payload.components.forEach((component, index) => {
                                    // Add _method to component
                                    if (!component._method) {
                                        component._method = 'POST';
                                        console.log(`Added _method to component ${index}`);
                                    }

                                    // Fix calls array
                                    if (component.calls && Array.isArray(component.calls)) {
                                        component.calls.forEach((call, callIndex) => {
                                            if (!call._method) {
                                                call._method = 'POST';
                                                console.log(
                                                    `Added _method to component ${index} call ${callIndex}`
                                                );
                                            }
                                            // Ensure method property exists
                                            if (!call.method && call.params && call.params[
                                                    0]) {
                                                call.method = call.params[0];
                                                console.log(
                                                    `Added method property to call: ${call.method}`
                                                );
                                            }
                                        });
                                    }

                                    // Fix updates array
                                    if (component.updates && Array.isArray(component.updates)) {
                                        component.updates.forEach((update, updateIndex) => {
                                            if (!update._method) {
                                                update._method = 'POST';
                                                console.log(
                                                    `Added _method to component ${index} update ${updateIndex}`
                                                );
                                            }
                                        });
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }

        // iOS-safe Livewire action caller
        function safeCallLivewireAction(actionName, recordId) {
            try {
                if (isIOS()) {
                    console.log('iOS detected, using safe action caller for:', actionName, recordId);

                    // Multiple approaches for iOS compatibility
                    const wireElement = document.querySelector('[wire\\:id]');
                    if (wireElement && window.Livewire) {
                        const wireId = wireElement.getAttribute('wire:id');
                        const component = window.Livewire.find(wireId);

                        if (component) {
                            // Chrome iOS specific handling
                            const isChrome = /CriOS/.test(navigator.userAgent);

                            if (isChrome) {
                                // Chrome iOS needs special FormData handling
                                const originalFormData = window.FormData;
                                window.FormData = function(...args) {
                                    const formData = new originalFormData(...args);
                                    const originalAppend = formData.append;

                                    formData.append = function(name, value, filename) {
                                        console.log('FormData append:', name, value);
                                        return originalAppend.call(this, name, value, filename);
                                    };

                                    // Ensure _method is always present
                                    if (!formData.has('_method')) {
                                        formData.append('_method', 'POST');
                                    }

                                    return formData;
                                };

                                // Restore FormData after action
                                setTimeout(() => {
                                    window.FormData = originalFormData;
                                }, 2000);
                            }

                            // Enhanced fetch override for all iOS browsers
                            const originalFetch = window.fetch;
                            window.fetch = function(url, options = {}) {
                                console.log('Fetch intercepted:', url, options);

                                if (options.method === 'POST') {
                                    // Handle Livewire JSON payload specifically
                                    if (typeof options.body === 'string' && options.headers && options.headers[
                                            'X-Livewire'] !== undefined) {
                                        try {
                                            const payload = JSON.parse(options.body);
                                            console.log('Original Livewire payload:', payload);

                                            // Ensure _method exists in the main payload
                                            if (!payload._method) {
                                                payload._method = 'POST';
                                                console.log('Added _method to main Livewire payload');
                                            }

                                            // Ensure _method exists in each component
                                            if (payload.components && Array.isArray(payload.components)) {
                                                payload.components.forEach((component, index) => {
                                                    if (component.calls && Array.isArray(component.calls)) {
                                                        component.calls.forEach((call, callIndex) => {
                                                            if (!call._method) {
                                                                call._method = 'POST';
                                                                console.log(
                                                                    `Added _method to component ${index} call ${callIndex}`
                                                                );
                                                            }
                                                        });
                                                    }
                                                    if (!component._method) {
                                                        component._method = 'POST';
                                                        console.log(`Added _method to component ${index}`);
                                                    }
                                                });
                                            }

                                            options.body = JSON.stringify(payload);
                                            console.log('Modified Livewire payload:', payload);
                                        } catch (e) {
                                            console.error('Error parsing Livewire JSON:', e);
                                        }
                                    }

                                    // Handle regular FormData
                                    else if (options.body instanceof FormData) {
                                        if (!options.body.has('_method')) {
                                            options.body.append('_method', 'POST');
                                        }
                                        console.log('Added _method to FormData');
                                    }

                                    // Ensure headers include CSRF token
                                    if (!options.headers) {
                                        options.headers = {};
                                    }

                                    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute(
                                        'content');
                                    if (csrfToken && !options.headers['X-CSRF-TOKEN']) {
                                        options.headers['X-CSRF-TOKEN'] = csrfToken;
                                    }
                                }

                                return originalFetch.call(this, url, options);
                            };

                            // Call the action
                            component.call('mountTableAction', actionName, recordId);

                            // Restore original fetch after a delay
                            setTimeout(() => {
                                window.fetch = originalFetch;
                            }, 2000);
                        }
                    }
                } else {
                    // Standard approach for non-iOS
                    $wire.mountTableAction(actionName, recordId);
                }
            } catch (error) {
                console.error('Error calling Livewire action:', error);
                // Fallback to standard approach
                try {
                    $wire.mountTableAction(actionName, recordId);
                } catch (fallbackError) {
                    console.error('Fallback also failed:', fallbackError);
                    alert('Terjadi kesalahan saat memproses aksi. Silakan refresh halaman dan coba lagi.');
                }
            }
        }

        function updateStatus(deliveryId) {
            // Trigger Filament action for status update
            safeCallLivewireAction('update_status', deliveryId);
        }

        function approveAllowance(deliveryId) {
            // Trigger Filament action for allowance approval
            safeCallLivewireAction('approve_allowance', deliveryId);
        }

        function approveDelivery(deliveryId) {
            // Trigger Filament action for delivery approval
            safeCallLivewireAction('approve_delivery', deliveryId);
        }

        // Global iOS Livewire fix - Apply BEFORE any other handlers
        if (isIOS()) {
            console.log('iOS device detected, applying comprehensive fixes');

            // Global Livewire request interceptor
            document.addEventListener('livewire:init', function() {
                console.log('Setting up global iOS Livewire fixes');

                if (window.Livewire && window.Livewire.hook) {
                    // Hook ALL Livewire requests
                    window.Livewire.hook('request', ({
                        uri,
                        options,
                        payload,
                        respond,
                        succeed,
                        fail
                    }) => {
                        console.log('🔧 iOS Livewire request intercepted:', {
                            uri,
                            options,
                            payload
                        });

                        // Comprehensive payload fixing
                        if (payload && typeof payload === 'object') {
                            let modified = false;

                            // 1. Fix main payload
                            if (!payload._method) {
                                payload._method = 'POST';
                                modified = true;
                                console.log('✅ Added _method to main payload');
                            }

                            // 2. Fix components
                            if (payload.components && Array.isArray(payload.components)) {
                                payload.components.forEach((component, compIndex) => {
                                    if (!component._method) {
                                        component._method = 'POST';
                                        modified = true;
                                        console.log(`✅ Added _method to component ${compIndex}`);
                                    }

                                    // 3. Fix calls (most important for actions)
                                    if (component.calls && Array.isArray(component.calls)) {
                                        component.calls.forEach((call, callIndex) => {
                                            if (!call._method) {
                                                call._method = 'POST';
                                                modified = true;
                                                console.log(
                                                    `✅ Added _method to call ${compIndex}.${callIndex}`
                                                    );
                                            }

                                            // Ensure method property exists (critical!)
                                            if (!call.method) {
                                                if (call.params && call.params.length > 0) {
                                                    call.method = call.params[0];
                                                    console.log(
                                                        `✅ Added method property: ${call.method}`
                                                        );
                                                } else {
                                                    call.method = 'unknown';
                                                    console.log(
                                                        `⚠️ Added fallback method property`
                                                        );
                                                }
                                                modified = true;
                                            }
                                        });
                                    }

                                    // 4. Fix updates
                                    if (component.updates && Array.isArray(component.updates)) {
                                        component.updates.forEach((update, updateIndex) => {
                                            if (!update._method) {
                                                update._method = 'POST';
                                                modified = true;
                                                console.log(
                                                    `✅ Added _method to update ${compIndex}.${updateIndex}`
                                                    );
                                            }
                                        });
                                    }
                                });
                            }

                            if (modified) {
                                console.log('🎯 iOS payload fixed:', payload);
                            }
                        }
                    });

                    // Hook responses to catch errors
                    window.Livewire.hook('response', ({
                        status,
                        response
                    }) => {
                        if (status >= 400) {
                            console.error('❌ iOS Livewire error response:', {
                                status,
                                response
                            });
                        } else {
                            console.log('✅ iOS Livewire success response:', status);
                        }
                    });
                }
            });
        }

        // iOS-specific form submission handler
        if (isIOS()) {
            console.log('iOS device detected, applying additional fixes');

            document.addEventListener('DOMContentLoaded', function() {
                // Chrome iOS specific debugging
                const isChrome = /CriOS/.test(navigator.userAgent);
                if (isChrome) {
                    console.log('Chrome iOS detected, applying Chrome-specific fixes');

                    // Monitor all network requests
                    const originalXHR = window.XMLHttpRequest;
                    window.XMLHttpRequest = function() {
                        const xhr = new originalXHR();
                        const originalSend = xhr.send;

                        xhr.send = function(data) {
                            console.log('XHR send:', data);
                            if (data instanceof FormData) {
                                console.log('FormData entries:');
                                for (let pair of data.entries()) {
                                    console.log(pair[0] + ': ' + pair[1]);
                                }
                            }
                            return originalSend.call(this, data);
                        };

                        return xhr;
                    };
                }

                // Override form submissions for iOS
                document.addEventListener('submit', function(e) {
                    const form = e.target;
                    console.log('Form submit intercepted:', form);

                    if (form && form.method && form.method.toLowerCase() === 'post') {
                        // Ensure _method field exists for iOS
                        let methodField = form.querySelector('input[name="_method"]');
                        if (!methodField) {
                            methodField = document.createElement('input');
                            methodField.type = 'hidden';
                            methodField.name = '_method';
                            methodField.value = 'POST';
                            form.appendChild(methodField);
                            console.log('Added _method field to form');
                        }

                        // Ensure CSRF token exists
                        let csrfField = form.querySelector('input[name="_token"]');
                        if (!csrfField) {
                            const csrfToken = document.querySelector('meta[name="csrf-token"]')
                                ?.getAttribute('content');
                            if (csrfToken) {
                                csrfField = document.createElement('input');
                                csrfField.type = 'hidden';
                                csrfField.name = '_token';
                                csrfField.value = csrfToken;
                                form.appendChild(csrfField);
                                console.log('Added CSRF token to form');
                            }
                        }
                    }
                });

                // Override Livewire form submissions
                document.addEventListener('livewire:init', function() {
                    console.log('Livewire initialized, setting up hooks');

                    if (window.Livewire && window.Livewire.hook) {
                        window.Livewire.hook('request', ({
                            uri,
                            options,
                            payload,
                            respond,
                            succeed,
                            fail
                        }) => {
                            console.log('Livewire request hook:', {
                                uri,
                                options,
                                payload
                            });

                            // Ensure _method is included in payload for iOS
                            if (payload && typeof payload === 'object') {
                                if (!payload._method && options.method === 'POST') {
                                    payload._method = 'POST';
                                    console.log('Added _method to Livewire payload');
                                }

                                // Ensure CSRF token is present
                                if (!payload._token) {
                                    const csrfToken = document.querySelector(
                                        'meta[name="csrf-token"]')?.getAttribute('content');
                                    if (csrfToken) {
                                        payload._token = csrfToken;
                                        console.log('Added CSRF token to Livewire payload');
                                    }
                                }
                            }
                        });

                        // Hook into response to log errors
                        window.Livewire.hook('response', ({
                            status,
                            response
                        }) => {
                            if (status >= 400) {
                                console.error('Livewire error response:', {
                                    status,
                                    response
                                });
                            }
                        });
                    }
                });
            });
        }
    </script>
</x-filament-panels::page>
